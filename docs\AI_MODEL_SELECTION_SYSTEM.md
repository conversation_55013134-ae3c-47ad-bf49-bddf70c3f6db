# AI Model Selection System

## Overview

The AI Model Selection System is a comprehensive solution designed to intelligently select the most cost-effective AI model for each task while maintaining quality standards. This system addresses the issue of using expensive models like Claude 3 Opus for all tasks by implementing dynamic model selection based on task complexity and requirements.

## Problem Solved

**Before**: The system was hardcoded to use Claude 3 Opus ($15-75 per 1M tokens) for all Lead Developer tasks, resulting in extremely high AI costs.

**After**: The system now intelligently selects from 9 different AI models based on task complexity, requirements, and cost optimization preferences, potentially reducing costs by 80-95% for simple tasks.

## Architecture

### Core Components

1. **AI Models Configuration** (`backend/ai-models-config.js`)
   - Comprehensive database of 9 AI models with detailed specifications
   - Cost per 1K tokens (input/output)
   - Capability ratings (1-10) for different skills
   - Context windows, speed, reliability metrics
   - Best use cases and tier classifications

2. **AI Task Analyzer** (`backend/ai-task-analyzer.js`)
   - Uses Claude 3 Haiku (cheapest model) to analyze task complexity
   - Evaluates tasks on complexity levels (1=simple, 2=medium, 3=complex)
   - Determines required capabilities and estimated token usage
   - Provides fallback analysis if automated analysis fails

3. **AI Model Selector** (`backend/ai-model-selector.js`)
   - Core intelligence engine that selects optimal models
   - Implements three optimization strategies: aggressive, balanced, quality
   - Tracks costs and provides detailed selection reasoning
   - Supports configuration and cost constraints

4. **Updated AI Services** (`backend/ai.js`)
   - New intelligent completion functions for different task types
   - Backward compatibility with existing functions
   - Cost tracking and model selection metadata
   - Fallback mechanisms for reliability

## Available Models

| Model | Provider | Tier | Input Cost | Output Cost | Best For |
|-------|----------|------|------------|-------------|----------|
| Claude 3 Opus | Anthropic | Premium | $0.015 | $0.075 | Complex reasoning, critical code |
| Claude 3 Sonnet | Anthropic | Balanced | $0.003 | $0.015 | General development tasks |
| Claude 3 Haiku | Anthropic | Budget | $0.00025 | $0.00125 | Simple tasks, analysis |
| GPT-4 Turbo | OpenAI | Premium | $0.01 | $0.03 | Complex problem solving |
| GPT-3.5 Turbo | OpenAI | Efficient | $0.0005 | $0.0015 | Text processing, simple code |
| Llama 3.1 405B | Meta | Premium | $0.005 | $0.015 | Open-source alternative |
| Llama 3.1 70B | Meta | Balanced | $0.0009 | $0.0009 | Cost-effective reasoning |
| Llama 3.1 8B | Meta | Budget | $0.0002 | $0.0002 | Ultra-low cost tasks |
| Mistral Large | Mistral | Balanced | $0.004 | $0.012 | European alternative |

## Cost Optimization Strategies

### 1. Aggressive Cost Optimization
- **Priority**: Minimize cost above all
- **Weight**: 70% cost, 20% capability, 10% reliability
- **Use Case**: Budget-conscious environments
- **Potential Savings**: 85-95% cost reduction

### 2. Balanced Optimization (Default)
- **Priority**: Balance cost and quality
- **Weight**: 40% cost, 40% capability, 20% reliability
- **Use Case**: Most production environments
- **Potential Savings**: 60-80% cost reduction

### 3. Quality Optimization
- **Priority**: Maximize capability and reliability
- **Weight**: 20% cost, 50% capability, 30% reliability
- **Use Case**: Critical applications
- **Potential Savings**: 30-50% cost reduction

## Task Types and Model Selection

### User Story Refinement
- **Complexity**: Medium (2/3)
- **Required Capabilities**: Reasoning, text processing
- **Typical Model**: Claude 3 Sonnet or GPT-3.5 Turbo
- **Cost Reduction**: ~80% vs Opus

### Task Breakdown
- **Complexity**: Medium (2/3)
- **Required Capabilities**: Reasoning, complex analysis
- **Typical Model**: Claude 3 Sonnet or Llama 3.1 70B
- **Cost Reduction**: ~75% vs Opus

### Code Generation
- **Complexity**: High (3/3)
- **Required Capabilities**: Code generation, reasoning
- **Typical Model**: Claude 3 Sonnet or GPT-4 Turbo
- **Cost Reduction**: ~60% vs Opus

### Acceptance Criteria Validation
- **Complexity**: High (3/3)
- **Required Capabilities**: Complex analysis, reasoning
- **Typical Model**: Claude 3 Sonnet or GPT-4 Turbo
- **Cost Reduction**: ~65% vs Opus

### Text Processing
- **Complexity**: Low (1/3)
- **Required Capabilities**: Text processing
- **Typical Model**: Claude 3 Haiku or GPT-3.5 Turbo
- **Cost Reduction**: ~90% vs Opus

## API Endpoints

### Model Management
- `GET /api/ai/models` - Get available models and specifications
- `GET /api/ai/costs` - Get cost statistics and usage data
- `PATCH /api/ai/model-selector/config` - Update selection configuration
- `POST /api/ai/costs/reset` - Reset cost tracking
- `POST /api/ai/models/recommend` - Get model recommendation for a task

### Usage Examples

```javascript
// Get model recommendation
const response = await fetch('/api/ai/models/recommend', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    taskType: 'code_generation',
    taskDescription: 'Create a React component for user authentication',
    context: { complexity: 3, priority: 'high' }
  })
});

// Update configuration
await fetch('/api/ai/model-selector/config', {
  method: 'PATCH',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    config: {
      costOptimization: 'aggressive',
      maxCostPerTask: 0.05,
      qualityThreshold: 6
    }
  })
});
```

## Frontend Interface

### AI Model Manager (`/ai-models`)
- **Cost Statistics Dashboard**: Real-time cost tracking and analytics
- **Model Configuration**: Adjust optimization strategies and constraints
- **Available Models Grid**: Detailed view of all models with capabilities
- **Usage Analytics**: Model usage patterns and cost breakdown by complexity

### Features
- Interactive model comparison with capability ratings
- Cost tracking with complexity-based breakdown
- Configuration management for optimization strategies
- Model usage analytics and recommendations

## Integration Points

### Updated Components
1. **ProductOwnerAgent**: Now uses `getUserStoryRefinementCompletion()`
2. **ScrumMasterAgent**: Now uses `getTaskBreakdownCompletion()`
3. **LeadDeveloperAgent**: Now uses `getCodeGenerationCompletion()`
4. **AcceptanceCriteriaValidator**: Now uses `getAcceptanceCriteriaValidationCompletion()`

### Backward Compatibility
- All existing functions (`getLeadDeveloperCompletion`, `getProfessionalWorkerCompletion`) still work
- Legacy functions now use intelligent model selection internally
- No breaking changes to existing code

## Cost Impact Analysis

### Example Scenarios

**Scenario 1: Simple User Story Refinement**
- **Before**: Claude 3 Opus - $0.075 per task
- **After**: Claude 3 Haiku - $0.008 per task
- **Savings**: 89% cost reduction

**Scenario 2: Medium Task Breakdown**
- **Before**: Claude 3 Opus - $0.045 per task
- **After**: Claude 3 Sonnet - $0.012 per task
- **Savings**: 73% cost reduction

**Scenario 3: Complex Code Generation**
- **Before**: Claude 3 Opus - $0.120 per task
- **After**: Claude 3 Sonnet - $0.030 per task
- **Savings**: 75% cost reduction

### Monthly Cost Projections
- **100 tasks/month with old system**: ~$60-120
- **100 tasks/month with new system**: ~$12-25
- **Annual savings**: $576-1,140 (80-90% reduction)

## Configuration Options

### Model Selector Configuration
```javascript
{
  costOptimization: 'balanced',    // 'aggressive', 'balanced', 'quality'
  maxCostPerTask: 0.10,           // Maximum cost per task in USD
  qualityThreshold: 7,            // Minimum capability score (1-10)
  fallbackModel: 'claude-3-haiku', // Fallback when selection fails
  enableFallback: true            // Enable fallback mechanism
}
```

### Monitoring and Analytics
- Real-time cost tracking per task
- Model usage statistics
- Cost breakdown by complexity level
- Performance metrics and selection reasoning
- Historical cost trends and projections

## Future Enhancements

1. **Machine Learning Model Selection**: Train ML models on historical performance data
2. **Custom Model Integration**: Support for custom fine-tuned models
3. **A/B Testing Framework**: Compare model performance across different tasks
4. **Advanced Cost Optimization**: Dynamic pricing and bulk discount integration
5. **Quality Feedback Loop**: Incorporate user feedback to improve model selection

## Getting Started

1. **Access the AI Model Manager**: Navigate to `/ai-models` in the application
2. **Review Current Configuration**: Check the default balanced optimization settings
3. **Monitor Costs**: Watch real-time cost statistics as tasks are processed
4. **Adjust Settings**: Modify optimization strategy based on your priorities
5. **Analyze Usage**: Review model usage patterns and cost breakdowns

The system is designed to work automatically with minimal configuration required. The default balanced optimization strategy provides excellent cost savings while maintaining quality standards.
