import { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import ProjectSidebar from './ProjectSidebar';
import StoriesTable from './StoriesTable';
import PropertiesPanel from './PropertiesPanel';
import SprintManager from './SprintManager';
import UserStoryModal from './UserStoryModal';
import ProjectSettings from './ProjectSettings';
import TeamView from './TeamView';
import ConfirmationModal from './ConfirmationModal';
import './ProjectDetail.css';

const ProjectDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const [project, setProject] = useState(null);
  const [userStories, setUserStories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeView, setActiveView] = useState(location.state?.activeView || 'stories');
  const [selectedStory, setSelectedStory] = useState(null);
  const [showStoryModal, setShowStoryModal] = useState(false);
  const [editingStory, setEditingStory] = useState(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showStoryDeleteConfirm, setShowStoryDeleteConfirm] = useState(false);
  const [storyToDelete, setStoryToDelete] = useState(null);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    fetchProject();
    fetchUserStories();
  }, [id]);

  // Handle navigation state changes
  useEffect(() => {
    if (location.state?.activeView) {
      setActiveView(location.state.activeView);
      // Clear the state to prevent issues with browser back/forward
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  const fetchProject = async () => {
    try {
      const response = await fetch(`/api/projects`);
      const data = await response.json();
      const foundProject = data.projects.find(p => p.id === id);
      setProject(foundProject);
    } catch (error) {
      console.error('Error fetching project:', error);
    }
  };

  const fetchUserStories = async () => {
    try {
      const response = await fetch(`/api/projects/${id}/stories`);
      const data = await response.json();
      setUserStories(data.stories || []);
    } catch (error) {
      console.error('Error fetching user stories:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteProject = () => {
    setShowDeleteConfirm(true);
  };

  const confirmDeleteProject = async () => {
    try {
      const response = await fetch(`/api/projects/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        navigate('/');
      } else {
        const errorData = await response.json().catch(() => ({}));
        setErrorMessage(errorData.error || 'Failed to delete project');
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error('Error deleting project:', error);
      setErrorMessage('Failed to delete project. Please try again.');
      setShowErrorModal(true);
    } finally {
      setShowDeleteConfirm(false);
    }
  };

  // Story handlers
  const handleStorySelect = (story) => {
    setSelectedStory(story);
  };

  const handleStoryUpdate = async (storyId, updates) => {
    try {
      const response = await fetch(`/api/projects/${id}/stories/${storyId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (response.ok) {
        const updatedStory = await response.json();
        setUserStories(stories =>
          stories.map(story =>
            story.id === storyId ? { ...story, ...updatedStory } : story
          )
        );
        if (selectedStory?.id === storyId) {
          setSelectedStory({ ...selectedStory, ...updatedStory });
        }
      }
    } catch (error) {
      console.error('Error updating story:', error);
    }
  };

  const handleStoryDelete = (storyId) => {
    const story = userStories.find(s => s.id === storyId);
    setStoryToDelete(story);
    setShowStoryDeleteConfirm(true);
  };

  const confirmStoryDelete = async () => {
    if (!storyToDelete) return;

    try {
      const response = await fetch(`/api/projects/${id}/stories/${storyToDelete.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setUserStories(stories => stories.filter(story => story.id !== storyToDelete.id));
        if (selectedStory?.id === storyToDelete.id) {
          setSelectedStory(null);
        }
      }
    } catch (error) {
      console.error('Error deleting story:', error);
    } finally {
      setShowStoryDeleteConfirm(false);
      setStoryToDelete(null);
    }
  };

  const handleStoriesReorder = (newOrder) => {
    setUserStories(newOrder);
    // TODO: Send reorder to backend if needed
  };

  // Calculate stats for sidebar
  const getStoryStats = () => {
    return {
      total: userStories.length,
      completed: userStories.filter(s => s.status === 'done').length,
      inProgress: userStories.filter(s => s.status === 'in_progress' || s.status === 'testing').length,
      backlog: userStories.filter(s => s.status === 'backlog' || s.status === 'ready').length,
    };
  };

  // Story modal handlers
  const handleStoryCreate = () => {
    setEditingStory(null);
    setShowStoryModal(true);
  };

  const handleStorySave = (savedStory) => {
    if (editingStory) {
      setUserStories(prev => prev.map(story =>
        story.id === editingStory.id ? savedStory : story
      ));
    } else {
      setUserStories(prev => [savedStory, ...prev]);
    }
    fetchUserStories(); // Refresh to get latest data
  };

  // Loading and error states
  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading project...</p>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="error-container">
        <h2>Project not found</h2>
        <button onClick={() => navigate('/')} className="btn-primary">
          <ArrowLeft size={20} />
          Back to Dashboard
        </button>
      </div>
    );
  }

  // Render the new three-panel layout
  return (
    <div className="project-detail-modern">
      {/* Left Sidebar */}
      <ProjectSidebar
        activeView={activeView}
        onViewChange={(view) => {
          if (view === 'reports') {
            navigate(`/projects/${id}/reports`);
          } else {
            setActiveView(view);
          }
        }}
        project={project}
        stats={getStoryStats()}
        onBack={() => navigate('/')}
        onCreateStory={handleStoryCreate}
      />

      {/* Main Content Area */}
      <div className="main-content">
        {activeView === 'stories' && (
          <StoriesTable
            stories={userStories}
            onStoriesReorder={handleStoriesReorder}
            onStorySelect={handleStorySelect}
            selectedStory={selectedStory}
            onStoryUpdate={handleStoryUpdate}
            projectId={id}
          />
        )}

        {activeView === 'sprints' && (
          <SprintManager
            projectId={id}
            onStorySelect={handleStorySelect}
            selectedStory={selectedStory}
          />
        )}

        {activeView === 'board' && (
          <div className="coming-soon">
            <h2>Board View</h2>
            <p>Kanban board view coming soon...</p>
          </div>
        )}

        {activeView === 'reports' && (
          <div className="coming-soon">
            <h2>Reports</h2>
            <p>Analytics and reports coming soon...</p>
          </div>
        )}

        {activeView === 'team' && (
          <TeamView projectId={id} />
        )}

        {activeView === 'settings' && (
          <ProjectSettings
            project={project}
            onDeleteProject={handleDeleteProject}
          />
        )}
      </div>

      {/* Right Properties Panel */}
      {selectedStory && (
        <PropertiesPanel
          story={selectedStory}
          onClose={() => setSelectedStory(null)}
          onUpdate={handleStoryUpdate}
          onDelete={handleStoryDelete}
        />
      )}

      {/* Modals */}
      <UserStoryModal
        isOpen={showStoryModal}
        onClose={() => setShowStoryModal(false)}
        onSave={handleStorySave}
        story={editingStory}
        projectId={id}
      />

      <ConfirmationModal
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={confirmDeleteProject}
        title="Delete Project"
        message={`Are you sure you want to delete "${project?.name}"? This will permanently delete all user stories, sprints, comments, and project data. This action cannot be undone.`}
        confirmText="Delete Project"
        cancelText="Cancel"
        type="danger"
      />

      <ConfirmationModal
        isOpen={showStoryDeleteConfirm}
        onClose={() => {
          setShowStoryDeleteConfirm(false);
          setStoryToDelete(null);
        }}
        onConfirm={confirmStoryDelete}
        title="Delete User Story"
        message={`Are you sure you want to delete "${storyToDelete?.title}"? This action cannot be undone.`}
        confirmText="Delete Story"
        cancelText="Cancel"
        type="danger"
      />

      <ConfirmationModal
        isOpen={showErrorModal}
        onClose={() => setShowErrorModal(false)}
        onConfirm={() => setShowErrorModal(false)}
        title="Error"
        message={errorMessage}
        confirmText="OK"
        cancelText=""
        type="danger"
      />
    </div>
  );
};

export default ProjectDetail;
