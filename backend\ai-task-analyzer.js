/**
 * AI Task Analyzer
 * Uses a cheap model to analyze task complexity and requirements
 * to determine the optimal AI model for each specific task
 */

const axios = require('axios');
const { TASK_COMPLEXITY, TASK_REQUIREMENTS } = require('./ai-models-config');

class AITaskAnalyzer {
  constructor() {
    this.analyzerModel = 'anthropic/claude-3-haiku'; // Use cheapest model for analysis
    this.apiKey = process.env.OPENROUTER_API_KEY;
    this.siteUrl = 'http://localhost:3000';
    this.siteName = 'Quarrel - Project Management Platform';
  }

  /**
   * Analyze a task to determine its complexity and requirements
   * @param {string} taskType - Type of task (e.g., 'user_story_refinement', 'code_generation')
   * @param {string} taskDescription - Description of the specific task
   * @param {Object} context - Additional context (user story, project info, etc.)
   * @returns {Object} Analysis result with complexity, requirements, and recommendations
   */
  async analyzeTask(taskType, taskDescription, context = {}) {
    try {
      const analysisPrompt = this.buildAnalysisPrompt(taskType, taskDescription, context);
      const response = await this.callAnalyzerModel(analysisPrompt);
      return this.parseAnalysisResponse(response);
    } catch (error) {
      console.error('Task analysis failed:', error);
      // Fallback to medium complexity if analysis fails
      return this.getDefaultAnalysis(taskType);
    }
  }

  /**
   * Build the analysis prompt for the task analyzer
   */
  buildAnalysisPrompt(taskType, taskDescription, context) {
    return `You are a task complexity analyzer. Your job is to analyze the complexity of a task, NOT to perform the task itself.

IMPORTANT: Do NOT generate user stories, code, or content. Only analyze the task complexity.

Task Type: ${taskType}
Task Description: ${taskDescription}
Context: ${JSON.stringify(context, null, 2)}

Analyze this task and determine:
1. Complexity level (1=simple, 2=medium, 3=complex)
2. Required capabilities
3. Estimated token usage
4. Quality requirements

Consider these factors:
- Simple (1): Basic text processing, formatting, simple validation, status updates
- Medium (2): User story refinement, task breakdown, standard code generation, documentation
- Complex (3): Advanced reasoning, complex architecture, critical business logic, multi-step analysis

Required capabilities:
- reasoning: Logical thinking and problem solving
- codeGeneration: Writing functional code
- creativity: Creative problem solving
- textProcessing: Text manipulation and formatting
- complexAnalysis: Deep analysis and evaluation
- multiStep: Multi-step reasoning and planning

CRITICAL: Respond with ONLY the JSON object below. Do not include any explanations, comments, or additional text before or after the JSON.

{
  "complexity": 1,
  "complexityReason": "Brief explanation of why this complexity level",
  "requiredCapabilities": ["reasoning", "codeGeneration"],
  "estimatedInputTokens": 500,
  "estimatedOutputTokens": 1000,
  "qualityRequirement": "high|medium|low",
  "timeConstraint": "urgent|normal|flexible",
  "riskLevel": "high|medium|low",
  "taskCategory": "text_processing|code_generation|analysis|creative|validation|multi_step"
}`;
  }

  /**
   * Call the analyzer model
   */
  async callAnalyzerModel(prompt) {
    const response = await axios.post('https://openrouter.ai/api/v1/chat/completions', {
      model: this.analyzerModel,
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.1 // Low temperature for consistent analysis
    }, {
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'HTTP-Referer': this.siteUrl,
        'X-Title': this.siteName,
        'Content-Type': 'application/json'
      }
    });

    return response.data.choices[0].message.content;
  }

  /**
   * Parse the analysis response from the model
   */
  parseAnalysisResponse(response) {
    try {
      // Clean the response to extract JSON - try multiple approaches
      let jsonString = null;

      // Method 1: Look for JSON between code blocks
      const codeBlockMatch = response.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
      if (codeBlockMatch) {
        jsonString = codeBlockMatch[1];
      } else {
        // Method 2: Find the first complete JSON object
        const startIndex = response.indexOf('{');
        if (startIndex !== -1) {
          let braceCount = 0;
          let endIndex = startIndex;

          for (let i = startIndex; i < response.length; i++) {
            if (response[i] === '{') braceCount++;
            if (response[i] === '}') braceCount--;
            if (braceCount === 0) {
              endIndex = i;
              break;
            }
          }

          if (braceCount === 0) {
            jsonString = response.substring(startIndex, endIndex + 1);
          }
        }
      }

      if (!jsonString) {
        throw new Error('No valid JSON found in response');
      }

      // Additional cleaning before parsing
      const cleanedJsonString = jsonString
        .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // Remove control characters
        .replace(/,\s*}/g, '}') // Remove trailing commas
        .replace(/,\s*]/g, ']'); // Remove trailing commas in arrays

      const analysis = JSON.parse(cleanedJsonString);

      // Validate the analysis
      if (!this.validateAnalysis(analysis)) {
        console.error('Analysis validation failed. Received:', analysis);
        console.error('Expected fields:', ['complexity', 'requiredCapabilities', 'estimatedInputTokens', 'estimatedOutputTokens']);
        throw new Error('Invalid analysis format');
      }

      return {
        ...analysis,
        timestamp: new Date().toISOString(),
        analyzerModel: this.analyzerModel
      };
    } catch (error) {
      console.error('Failed to parse analysis response:', error);
      console.error('Response preview:', response.substring(0, 500) + '...');

      // Check if the response looks like user stories instead of analysis
      if (response.includes('"stories"') || response.includes('"title"') || response.includes('As a ')) {
        console.warn('⚠️  AI returned user stories instead of task analysis. Using fallback analysis.');
        return this.getDefaultAnalysis('user_story_generation');
      }

      throw error;
    }
  }

  /**
   * Validate the analysis response
   */
  validateAnalysis(analysis) {
    const required = ['complexity', 'requiredCapabilities', 'estimatedInputTokens', 'estimatedOutputTokens'];
    return required.every(field => analysis.hasOwnProperty(field)) &&
           analysis.complexity >= 1 && analysis.complexity <= 3 &&
           Array.isArray(analysis.requiredCapabilities);
  }

  /**
   * Get default analysis if automated analysis fails
   */
  getDefaultAnalysis(taskType) {
    const defaults = {
      'user_story_refinement': {
        complexity: 2,
        complexityReason: 'Standard user story refinement task',
        requiredCapabilities: ['reasoning', 'textProcessing'],
        estimatedInputTokens: 800,
        estimatedOutputTokens: 600,
        qualityRequirement: 'medium',
        timeConstraint: 'normal',
        riskLevel: 'medium',
        taskCategory: 'analysis'
      },
      'code_generation': {
        complexity: 3,
        complexityReason: 'Code generation requires high precision',
        requiredCapabilities: ['codeGeneration', 'reasoning'],
        estimatedInputTokens: 1200,
        estimatedOutputTokens: 2000,
        qualityRequirement: 'high',
        timeConstraint: 'normal',
        riskLevel: 'high',
        taskCategory: 'code_generation'
      },
      'task_breakdown': {
        complexity: 2,
        complexityReason: 'Standard task breakdown',
        requiredCapabilities: ['reasoning', 'multiStep'],
        estimatedInputTokens: 600,
        estimatedOutputTokens: 800,
        qualityRequirement: 'medium',
        timeConstraint: 'normal',
        riskLevel: 'medium',
        taskCategory: 'analysis'
      },
      'acceptance_criteria_validation': {
        complexity: 3,
        complexityReason: 'Critical validation requires high accuracy',
        requiredCapabilities: ['reasoning', 'complexAnalysis'],
        estimatedInputTokens: 1500,
        estimatedOutputTokens: 1000,
        qualityRequirement: 'high',
        timeConstraint: 'normal',
        riskLevel: 'high',
        taskCategory: 'validation'
      },
      'text_processing': {
        complexity: 1,
        complexityReason: 'Simple text processing task',
        requiredCapabilities: ['textProcessing'],
        estimatedInputTokens: 300,
        estimatedOutputTokens: 400,
        qualityRequirement: 'low',
        timeConstraint: 'normal',
        riskLevel: 'low',
        taskCategory: 'text_processing'
      }
    };

    return {
      ...defaults[taskType] || defaults['user_story_refinement'],
      timestamp: new Date().toISOString(),
      analyzerModel: 'fallback',
      fallback: true
    };
  }

  /**
   * Analyze multiple tasks in batch
   */
  async analyzeBatch(tasks) {
    const results = [];
    for (const task of tasks) {
      try {
        const analysis = await this.analyzeTask(task.type, task.description, task.context);
        results.push({ ...task, analysis });
        
        // Small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.error(`Failed to analyze task ${task.id}:`, error);
        results.push({ 
          ...task, 
          analysis: this.getDefaultAnalysis(task.type),
          error: error.message 
        });
      }
    }
    return results;
  }

  /**
   * Get task complexity statistics
   */
  getComplexityStats(analyses) {
    const stats = {
      simple: 0,
      medium: 0,
      complex: 0,
      total: analyses.length
    };

    analyses.forEach(analysis => {
      switch (analysis.complexity) {
        case 1: stats.simple++; break;
        case 2: stats.medium++; break;
        case 3: stats.complex++; break;
      }
    });

    return {
      ...stats,
      percentages: {
        simple: (stats.simple / stats.total * 100).toFixed(1),
        medium: (stats.medium / stats.total * 100).toFixed(1),
        complex: (stats.complex / stats.total * 100).toFixed(1)
      }
    };
  }
}

module.exports = { AITaskAnalyzer };
