import React, { useState, useEffect } from 'react';
import { Calendar, Clock, Target, Users, Play, Pause, CheckCircle, ChevronDown, ChevronRight, BarChart3, TrendingUp, Edit, Bot, Square, AlertTriangle, GitBranch, MoreHorizontal } from 'lucide-react';
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors, DragOverlay, useDroppable } from '@dnd-kit/core';
import { arrayMove, SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import ConfirmationModal from './ConfirmationModal';
import SlideoutPanel from './SlideoutPanel';
import './SprintManager.css';

// Helper functions for story display
const getStatusIcon = (status) => {
  switch (status) {
    case 'done':
      return <CheckCircle className="status-icon done" size={14} />;
    case 'in_progress':
      return <Clock className="status-icon in-progress" size={14} />;
    case 'backlog':
      return <Clock className="status-icon backlog" size={14} />;
    default:
      return <Clock className="status-icon backlog" size={14} />;
  }
};

const getImplementationStatusBadge = (implementationStatus, version) => {
  switch (implementationStatus) {
    case 'implemented':
      return (
        <span className="implementation-badge implemented">
          <CheckCircle size={12} />
          Implemented
        </span>
      );
    case 'modified_after_implementation':
      return (
        <span className="implementation-badge modified">
          <AlertTriangle size={12} />
          Modified
        </span>
      );
    default:
      return (
        <span className="implementation-badge not-started">
          <Clock size={12} />
          Not Started
        </span>
      );
  }
};

const getPriorityColor = (priority) => {
  switch (priority) {
    case 'critical': return '#ef4444';
    case 'high': return '#f59e0b';
    case 'medium': return '#6b7280';
    case 'low': return '#10b981';
    default: return '#6b7280';
  }
};

// Draggable Story Component
const DraggableStory = ({ story, isDragging = false, onSelect, isSelected }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useSortable({ id: story.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const handleClick = (e) => {
    // Don't trigger selection if dragging
    if (e.defaultPrevented) return;
    if (onSelect) {
      onSelect(story);
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={`story-item draggable ${isSelected ? 'selected' : ''}`}
      onClick={handleClick}
    >
      <div className="story-drag-handle">
        <div className="drag-indicator">⋮⋮</div>
      </div>

      <div className="story-id">
        <span className="id-badge">{story.id.slice(-6)}</span>
      </div>

      <div className="story-title-section">
        <div className="story-title-content">
          <span className="story-title">{story.title}</span>
          {story.epic && <span className="epic-badge">{story.epic}</span>}
        </div>
      </div>

      <div className="story-status-section">
        <div className="status-display">
          {getStatusIcon(story.status)}
          <span className="status-text">{story.status.replace('_', ' ')}</span>
        </div>
      </div>

      <div className="story-priority-section">
        <span
          className="priority-badge"
          style={{ backgroundColor: getPriorityColor(story.priority) }}
        >
          {story.priority}
        </span>
      </div>

      <div className="story-points-section">
        <span className="points-badge">{story.story_points}</span>
      </div>

      <div className="story-implementation-section">
        {getImplementationStatusBadge(story.implementation_status, story.implemented_in_version)}
      </div>

      <div className="story-version-section">
        {story.implemented_in_version && (
          <span className="version-badge">
            <GitBranch size={12} />
            v{story.implemented_in_version}
          </span>
        )}
      </div>

      <div className="story-actions-section">
        <button className="action-btn" onClick={(e) => e.stopPropagation()}>
          <MoreHorizontal size={16} />
        </button>
      </div>
    </div>
  );
};

// Droppable Sprint Row Component
const DroppableSprintRow = ({ sprint, metrics, isExpanded, onToggle, onStatusUpdate, onStartProcessing, loading, children }) => {
  const {
    setNodeRef,
    isOver,
  } = useDroppable({
    id: `sprint-${sprint.id}`,
    data: { type: 'sprint', sprint }
  });

  return (
    <div
      ref={setNodeRef}
      className={`sprint-row ${isOver ? 'drop-target' : ''}`}
    >
      {children}
    </div>
  );
};

// Droppable Unassigned Area Component
const DroppableUnassignedArea = ({ children }) => {
  const {
    setNodeRef,
    isOver,
  } = useDroppable({
    id: 'unassigned',
    data: { type: 'unassigned' }
  });

  return (
    <div
      ref={setNodeRef}
      className={`unassigned-stories ${isOver ? 'drop-target' : ''}`}
    >
      {children}
    </div>
  );
};

const SprintManager = ({ projectId, onStorySelect, selectedStory }) => {
  const [sprints, setSprints] = useState([]);
  const [userStories, setUserStories] = useState([]);
  const [selectedSprint, setSelectedSprint] = useState(null);
  const [showCreateSprint, setShowCreateSprint] = useState(false);
  const [showEditSprint, setShowEditSprint] = useState(false);
  const [editingSprint, setEditingSprint] = useState(null);
  const [loading, setLoading] = useState(false);
  const [expandedSprints, setExpandedSprints] = useState(new Set());
  const [activeId, setActiveId] = useState(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [modalMessage, setModalMessage] = useState('');

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  useEffect(() => {
    fetchSprints();
    fetchUserStories();
  }, [projectId]);

  const fetchSprints = async () => {
    try {
      const response = await fetch(`http://localhost:3000/projects/${projectId}/sprints`);
      const data = await response.json();
      setSprints(data.sprints);
    } catch (error) {
      console.error('Error fetching sprints:', error);
    }
  };

  const fetchUserStories = async () => {
    try {
      const response = await fetch(`http://localhost:3000/projects/${projectId}/stories`);
      const data = await response.json();
      setUserStories(data.stories);
    } catch (error) {
      console.error('Error fetching user stories:', error);
    }
  };

  const createSprint = async (sprintData) => {
    try {
      const response = await fetch(`http://localhost:3000/projects/${projectId}/sprints`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(sprintData)
      });

      if (response.ok) {
        fetchSprints();
        setShowCreateSprint(false);
      }
    } catch (error) {
      console.error('Error creating sprint:', error);
    }
  };

  const updateSprint = async (sprintId, sprintData) => {
    try {
      const response = await fetch(`http://localhost:3000/projects/${projectId}/sprints/${sprintId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(sprintData)
      });

      if (response.ok) {
        fetchSprints();
        setShowEditSprint(false);
        setEditingSprint(null);
      }
    } catch (error) {
      console.error('Error updating sprint:', error);
    }
  };

  const openEditSprint = (sprint) => {
    setEditingSprint(sprint);
    setShowEditSprint(true);
  };

  const updateSprintStatus = async (sprintId, status) => {
    try {
      const response = await fetch(`http://localhost:3000/projects/${projectId}/sprints/${sprintId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status })
      });
      
      if (response.ok) {
        fetchSprints();
      }
    } catch (error) {
      console.error('Error updating sprint status:', error);
    }
  };

  const assignStoryToSprint = async (storyId, sprintId) => {
    try {
      const response = await fetch(`http://localhost:3000/projects/${projectId}/stories/${storyId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sprint_id: sprintId })
      });

      if (response.ok) {
        fetchUserStories();
      }
    } catch (error) {
      console.error('Error assigning story to sprint:', error);
    }
  };

  const removeStoryFromSprint = async (storyId) => {
    try {
      const response = await fetch(`http://localhost:3000/projects/${projectId}/stories/${storyId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sprint_id: null })
      });

      if (response.ok) {
        fetchUserStories();
      }
    } catch (error) {
      console.error('Error removing story from sprint:', error);
    }
  };

  const startSprintProcessing = async (sprintId) => {
    setLoading(true);
    try {
      const response = await fetch(`http://localhost:3000/projects/${projectId}/ai/process-sprint/${sprintId}`, {
        method: 'POST'
      });
      
      if (response.ok) {
        const result = await response.json();
        setModalMessage(`Sprint processing completed! ${result.result.processedStories} stories processed.`);
        setShowSuccessModal(true);
        fetchUserStories();
      }
    } catch (error) {
      console.error('Error starting sprint processing:', error);
      setModalMessage('Error starting sprint processing');
      setShowErrorModal(true);
    } finally {
      setLoading(false);
    }
  };

  const getSprintStories = (sprintId) => {
    return userStories.filter(story => story.sprint_id === sprintId);
  };

  const getUnassignedStories = () => {
    return userStories.filter(story => !story.sprint_id);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'planning': return <Calendar className="w-4 h-4" />;
      case 'active': return <Play className="w-4 h-4" />;
      case 'completed': return <CheckCircle className="w-4 h-4" />;
      default: return <Pause className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'planning': return 'status-planning';
      case 'active': return 'status-active';
      case 'completed': return 'status-completed';
      default: return 'status-default';
    }
  };

  const toggleSprintExpansion = (sprintId) => {
    const newExpanded = new Set(expandedSprints);
    if (newExpanded.has(sprintId)) {
      newExpanded.delete(sprintId);
    } else {
      newExpanded.add(sprintId);
    }
    setExpandedSprints(newExpanded);
  };

  const getSprintMetrics = (sprintId) => {
    const stories = getSprintStories(sprintId);
    const totalStories = stories.length;
    const completedStories = stories.filter(s => s.status === 'done').length;
    const inProgressStories = stories.filter(s => s.status === 'in_progress').length;
    const totalPoints = stories.reduce((sum, s) => sum + (s.story_points || 0), 0);
    const completedPoints = stories.filter(s => s.status === 'done').reduce((sum, s) => sum + (s.story_points || 0), 0);

    return {
      totalStories,
      completedStories,
      inProgressStories,
      totalPoints,
      completedPoints,
      completionPercentage: totalStories > 0 ? Math.round((completedStories / totalStories) * 100) : 0,
      pointsPercentage: totalPoints > 0 ? Math.round((completedPoints / totalPoints) * 100) : 0
    };
  };

  const handleDragStart = (event) => {
    setActiveId(event.active.id);
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;
    setActiveId(null);

    if (!over) return;

    const storyId = active.id;
    const overId = over.id;

    // If dropped on a sprint row, assign to that sprint
    if (overId.startsWith('sprint-')) {
      const sprintId = overId.replace('sprint-', '');
      assignStoryToSprint(storyId, sprintId);
    }
    // If dropped on unassigned area, remove from sprint
    else if (overId === 'unassigned') {
      removeStoryFromSprint(storyId);
    }
  };

  const findStoryById = (id) => {
    return userStories.find(story => story.id === id);
  };

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="sprint-manager">
        <div className="sprint-header">
          <h2>Sprints</h2>
          <button
            className="btn-primary btn-compact"
            onClick={() => setShowCreateSprint(true)}
          >
            New Sprint
          </button>
        </div>

        <div className="sprint-content">
          <SortableContext items={[...sprints.map(s => `sprint-${s.id}`), 'unassigned']}>
            <div className="sprints-list">
              <div className="sprint-header-row">
                <div className="sprint-header-cell"></div>
                <div className="sprint-header-cell">Sprint</div>
                <div className="sprint-header-cell">Status</div>
                <div className="sprint-header-cell">Scope</div>
                <div className="sprint-header-cell">Started</div>
                <div className="sprint-header-cell">Completed</div>
                <div className="sprint-header-cell">Actions</div>
              </div>
              {sprints.map(sprint => {
                const metrics = getSprintMetrics(sprint.id);
                const isExpanded = expandedSprints.has(sprint.id);

                return (
                  <DroppableSprintRow
                    key={sprint.id}
                    sprint={sprint}
                    metrics={metrics}
                    isExpanded={isExpanded}
                    onToggle={() => toggleSprintExpansion(sprint.id)}
                    onStatusUpdate={updateSprintStatus}
                    onStartProcessing={startSprintProcessing}
                    loading={loading}
                  >
                    <div className="sprint-main" onClick={() => toggleSprintExpansion(sprint.id)}>
                      <div className="sprint-left">
                        <button className="expand-btn">
                          {isExpanded ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
                        </button>
                        <div className="sprint-info">
                          <div className="sprint-name">{sprint.name}</div>
                          <div className="sprint-dates">
                            {new Date(sprint.start_date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - {new Date(sprint.end_date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                          </div>
                        </div>
                      </div>

                      <div className="sprint-right">
                        <div className="sprint-status-badge">
                          <span className={`status-indicator ${getStatusColor(sprint.status)}`}>
                            {sprint.status}
                          </span>
                        </div>

                        <div className="sprint-metrics">
                          <div className="metric">
                            <span className="metric-label">Scope</span>
                            <span className="metric-value">
                              <Target className="w-3 h-3" />
                              {metrics.totalStories}
                            </span>
                          </div>

                          <div className="metric">
                            <span className="metric-label">Started</span>
                            <span className="metric-value">
                              <TrendingUp className="w-3 h-3" />
                              {metrics.inProgressStories + metrics.completedStories} • {metrics.pointsPercentage}%
                            </span>
                          </div>

                          <div className="metric">
                            <span className="metric-label">Completed</span>
                            <span className="metric-value">
                              <CheckCircle className="w-3 h-3" />
                              {metrics.completedStories} • {metrics.completionPercentage}%
                            </span>
                          </div>
                        </div>

                        <div className="sprint-actions">
                          <Edit
                            className="action-icon action-icon-secondary"
                            onClick={(e) => { e.stopPropagation(); openEditSprint(sprint); }}
                            title="Edit Sprint"
                          />
                          {sprint.status === 'planning' && (
                            <>
                              <Play
                                className="action-icon action-icon-success"
                                onClick={(e) => { e.stopPropagation(); updateSprintStatus(sprint.id, 'active'); }}
                                title="Start Sprint"
                              />
                              <Bot
                                className={`action-icon action-icon-primary ${loading ? 'disabled' : ''}`}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (!loading) startSprintProcessing(sprint.id);
                                }}
                                title="AI Process Sprint"
                              />
                            </>
                          )}
                          {sprint.status === 'active' && (
                            <Square
                              className="action-icon action-icon-warning"
                              onClick={(e) => { e.stopPropagation(); updateSprintStatus(sprint.id, 'completed'); }}
                              title="Complete Sprint"
                            />
                          )}
                        </div>
                      </div>
                    </div>

                    {isExpanded && (
                      <div className="sprint-expanded">
                        {sprint.goals && (
                          <div className="sprint-goals">
                            <Target className="w-4 h-4" />
                            <span>{sprint.goals}</span>
                          </div>
                        )}

                        <div className="sprint-stories-expanded">
                          <h4>Stories ({getSprintStories(sprint.id).length})</h4>
                          <SortableContext items={getSprintStories(sprint.id).map(s => s.id)}>
                            <div className="story-list">
                              {getSprintStories(sprint.id).length > 0 ? (
                                getSprintStories(sprint.id).map(story => (
                                  <DraggableStory
                                    key={story.id}
                                    story={story}
                                    onSelect={onStorySelect}
                                    isSelected={selectedStory?.id === story.id}
                                  />
                                ))
                              ) : (
                                <div className="empty-stories">
                                  <span>No stories assigned to this sprint yet. Drag stories here to assign them.</span>
                                </div>
                              )}
                            </div>
                          </SortableContext>
                        </div>
                      </div>
                    )}
                  </DroppableSprintRow>
                );
              })}
            </div>

            <DroppableUnassignedArea>
              <h3>Unassigned Stories</h3>
              <SortableContext items={getUnassignedStories().map(s => s.id)}>
                <div className="story-list">
                  {getUnassignedStories().length > 0 ? (
                    getUnassignedStories().map(story => (
                      <DraggableStory
                        key={story.id}
                        story={story}
                        onSelect={onStorySelect}
                        isSelected={selectedStory?.id === story.id}
                      />
                    ))
                  ) : (
                    <div className="empty-unassigned">
                      Drag stories here to unassign them from sprints
                    </div>
                  )}
                </div>
              </SortableContext>
            </DroppableUnassignedArea>
          </SortableContext>
        </div>

        {showCreateSprint && (
          <CreateSprintModal
            onClose={() => setShowCreateSprint(false)}
            onSubmit={createSprint}
          />
        )}

        {showEditSprint && editingSprint && (
          <EditSprintModal
            sprint={editingSprint}
            onClose={() => {
              setShowEditSprint(false);
              setEditingSprint(null);
            }}
            onSubmit={(sprintData) => updateSprint(editingSprint.id, sprintData)}
          />
        )}
      </div>

      <DragOverlay>
        {activeId ? (
          <DraggableStory
            story={findStoryById(activeId)}
            isDragging
            onSelect={onStorySelect}
            isSelected={selectedStory?.id === activeId}
          />
        ) : null}
      </DragOverlay>

      {/* Success Modal */}
      <ConfirmationModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        onConfirm={() => setShowSuccessModal(false)}
        title="Success"
        message={modalMessage}
        confirmText="OK"
        cancelText=""
        type="default"
      />

      {/* Error Modal */}
      <ConfirmationModal
        isOpen={showErrorModal}
        onClose={() => setShowErrorModal(false)}
        onConfirm={() => setShowErrorModal(false)}
        title="Error"
        message={modalMessage}
        confirmText="OK"
        cancelText=""
        type="danger"
      />
    </DndContext>
  );
};

const CreateSprintModal = ({ onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    start_date: '',
    end_date: '',
    goals: '',
    capacity_hours: 40
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <SlideoutPanel
      isOpen={true}
      onClose={onClose}
      title="Create New Sprint"
      icon={Calendar}
      width="600px"
      className="medium"
    >
      <div className="slideout-content form-content">
        <form onSubmit={handleSubmit} className="sprint-modal-content">
          <div className="sprint-form-group">
            <label>Sprint Name *</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({...formData, name: e.target.value})}
              required
            />
          </div>
          <div className="sprint-form-group">
            <label>Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
            />
          </div>
          <div className="sprint-form-row">
            <div className="sprint-form-group">
              <label>Start Date *</label>
              <input
                type="date"
                value={formData.start_date}
                onChange={(e) => setFormData({...formData, start_date: e.target.value})}
                required
              />
            </div>
            <div className="sprint-form-group">
              <label>End Date *</label>
              <input
                type="date"
                value={formData.end_date}
                onChange={(e) => setFormData({...formData, end_date: e.target.value})}
                required
              />
            </div>
          </div>
          <div className="sprint-form-group">
            <label>Sprint Goals</label>
            <textarea
              value={formData.goals}
              onChange={(e) => setFormData({...formData, goals: e.target.value})}
              placeholder="What do you want to achieve in this sprint?"
            />
          </div>
          <div className="sprint-form-group">
            <label>Capacity (Hours)</label>
            <input
              type="number"
              value={formData.capacity_hours}
              onChange={(e) => setFormData({...formData, capacity_hours: parseInt(e.target.value)})}
              min="1"
            />
          </div>
          <div className="sprint-modal-actions">
            <button type="button" className="btn btn-secondary" onClick={onClose}>
              Cancel
            </button>
            <button type="submit" className="btn btn-primary">
              Create Sprint
            </button>
          </div>
        </form>
      </div>
    </SlideoutPanel>
  );
};

const EditSprintModal = ({ sprint, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    name: sprint.name || '',
    description: sprint.description || '',
    start_date: sprint.start_date ? sprint.start_date.split('T')[0] : '',
    end_date: sprint.end_date ? sprint.end_date.split('T')[0] : '',
    goals: sprint.goals || '',
    capacity_hours: sprint.capacity_hours || 40
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <SlideoutPanel
      isOpen={true}
      onClose={onClose}
      title="Edit Sprint"
      icon={Edit}
      width="600px"
      className="medium"
    >
      <div className="slideout-content form-content">
        <form onSubmit={handleSubmit} className="sprint-modal-content">
          <div className="sprint-form-group">
            <label>Sprint Name *</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({...formData, name: e.target.value})}
              required
            />
          </div>
          <div className="sprint-form-group">
            <label>Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              rows="3"
            />
          </div>
          <div className="sprint-form-row">
            <div className="sprint-form-group">
              <label>Start Date *</label>
              <input
                type="date"
                value={formData.start_date}
                onChange={(e) => setFormData({...formData, start_date: e.target.value})}
                required
              />
            </div>
            <div className="sprint-form-group">
              <label>End Date *</label>
              <input
                type="date"
                value={formData.end_date}
                onChange={(e) => setFormData({...formData, end_date: e.target.value})}
                required
              />
            </div>
          </div>
          <div className="sprint-form-group">
            <label>Sprint Goals</label>
            <textarea
              value={formData.goals}
              onChange={(e) => setFormData({...formData, goals: e.target.value})}
              placeholder="What do you want to achieve in this sprint?"
              rows="3"
            />
          </div>
          <div className="sprint-form-group">
            <label>Capacity (Hours)</label>
            <input
              type="number"
              value={formData.capacity_hours}
              onChange={(e) => setFormData({...formData, capacity_hours: parseInt(e.target.value)})}
              min="1"
            />
          </div>
          <div className="sprint-modal-actions">
            <button type="button" className="btn btn-secondary" onClick={onClose}>
              Cancel
            </button>
            <button type="submit" className="btn btn-primary">
              Update Sprint
            </button>
          </div>
        </form>
      </div>
    </SlideoutPanel>
  );
};

export default SprintManager;
