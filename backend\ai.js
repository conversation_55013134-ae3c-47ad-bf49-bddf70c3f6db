const axios = require('axios');
const { AIModelSelector } = require('./ai-model-selector');

// WARNING: This is a placeholder key. Replace with your actual OpenRouter API key.
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY || "YOUR_OPENROUTER_API_KEY";
const YOUR_SITE_URL = 'http://localhost:3000'; // or your actual site url
const YOUR_SITE_NAME = 'Quarrel - Project Management Platform';

// Initialize the intelligent model selector
const modelSelector = new AIModelSelector();

const callOpenRouter = async (messages, model, metadata = {}) => {
    if (OPENROUTER_API_KEY === "YOUR_OPENROUTER_API_KEY") {
        console.error("OpenRouter API key is not set. Please set the OPENROUTER_API_KEY environment variable or replace the placeholder in backend/ai.js");
        throw new Error("OpenRouter API key not set");
    }

    try {
        console.log(`🤖 Using AI model: ${model} for task: ${metadata.taskType || 'unknown'}`);
        if (metadata.estimatedCost) {
            console.log(`💰 Estimated cost: $${metadata.estimatedCost.toFixed(4)}`);
        }

        const response = await axios.post('https://openrouter.ai/api/v1/chat/completions', {
            model: model,
            messages: messages,
        }, {
            headers: {
                'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
                'HTTP-Referer': YOUR_SITE_URL,
                'X-Title': YOUR_SITE_NAME,
                'Content-Type': 'application/json'
            }
        });
        return response.data.choices[0].message.content;
    } catch (error) {
        console.error('Error calling OpenRouter API:', error);
        throw error;
    }
};

/**
 * Intelligent completion function that selects the optimal model for each task
 * @param {string} prompt - The prompt to send to the AI
 * @param {string} taskType - Type of task (e.g., 'user_story_refinement', 'code_generation')
 * @param {Object} context - Additional context for model selection
 * @param {Object} options - Override options
 * @returns {Object} Response with content and metadata
 */
const getIntelligentCompletion = async (prompt, taskType, context = {}, options = {}) => {
    try {
        // Select the optimal model for this specific task
        const selection = await modelSelector.selectModel(taskType, prompt, context, options);

        // Call the selected model
        const messages = [{ role: 'user', content: prompt }];
        const content = await callOpenRouter(messages, selection.model, {
            taskType,
            estimatedCost: selection.estimatedCost,
            modelName: selection.modelInfo.name
        });

        return {
            content,
            metadata: {
                selectedModel: selection.model,
                modelName: selection.modelInfo.name,
                estimatedCost: selection.estimatedCost,
                reasoning: selection.reasoning,
                taskAnalysis: selection.analysis,
                alternatives: selection.alternatives
            }
        };
    } catch (error) {
        console.error('Intelligent completion failed:', error);
        // Fallback to a reliable model
        const fallbackModel = 'anthropic/claude-3-haiku';
        const messages = [{ role: 'user', content: prompt }];
        const content = await callOpenRouter(messages, fallbackModel, { taskType, fallback: true });

        return {
            content,
            metadata: {
                selectedModel: fallbackModel,
                modelName: 'Claude 3 Haiku (Fallback)',
                estimatedCost: 0.001,
                reasoning: 'Fallback due to model selection failure',
                fallback: true,
                error: error.message
            }
        };
    }
};

// Legacy functions for backward compatibility
const getLeadDeveloperCompletion = async (prompt, context = {}) => {
    const result = await getIntelligentCompletion(prompt, 'code_generation', context);
    return result.content;
};

const getProfessionalWorkerCompletion = async (prompt, context = {}) => {
    const result = await getIntelligentCompletion(prompt, 'text_processing', context);
    return result.content;
};

/**
 * Get AI cost statistics
 */
const getAICostStats = () => {
    return modelSelector.getCostStats();
};

/**
 * Update model selector configuration
 */
const updateModelSelectorConfig = (config) => {
    return modelSelector.updateConfig(config);
};

/**
 * Reset cost tracking
 */
const resetCostTracking = () => {
    return modelSelector.resetCostTracking();
};

/**
 * Get available models information
 */
const getAvailableModels = () => {
    const { AI_MODELS } = require('./ai-models-config');
    return AI_MODELS;
};

/**
 * Specialized completion functions for different task types
 */
const getUserStoryRefinementCompletion = async (prompt, context = {}) => {
    return await getIntelligentCompletion(prompt, 'user_story_refinement', context);
};

const getTaskBreakdownCompletion = async (prompt, context = {}) => {
    return await getIntelligentCompletion(prompt, 'task_breakdown', context);
};

const getCodeGenerationCompletion = async (prompt, context = {}) => {
    return await getIntelligentCompletion(prompt, 'code_generation', context);
};

const getAcceptanceCriteriaValidationCompletion = async (prompt, context = {}) => {
    return await getIntelligentCompletion(prompt, 'acceptance_criteria_validation', context);
};

const getTextProcessingCompletion = async (prompt, context = {}) => {
    return await getIntelligentCompletion(prompt, 'text_processing', context);
};

module.exports = {
    // Legacy functions (backward compatibility)
    getLeadDeveloperCompletion,
    getProfessionalWorkerCompletion,

    // New intelligent completion functions
    getIntelligentCompletion,
    getUserStoryRefinementCompletion,
    getTaskBreakdownCompletion,
    getCodeGenerationCompletion,
    getAcceptanceCriteriaValidationCompletion,
    getTextProcessingCompletion,

    // Utility functions
    getAICostStats,
    updateModelSelectorConfig,
    resetCostTracking,
    getAvailableModels,
    callOpenRouter,

    // Direct access to model selector
    modelSelector
};
