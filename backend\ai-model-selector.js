/**
 * AI Model Selector
 * Intelligently selects the most cost-effective AI model for each task
 * based on complexity analysis and requirements
 */

const { AI_MODELS, TASK_COMPLEXITY, TASK_REQUIREMENTS } = require('./ai-models-config');
const { AITaskAnalyzer } = require('./ai-task-analyzer');

class AIModelSelector {
  constructor() {
    this.taskAnalyzer = new AITaskAnalyzer();
    this.costTracker = {
      totalCost: 0,
      modelUsage: {},
      taskCosts: []
    };
    
    // Configuration for cost vs quality preferences
    this.config = {
      costOptimization: 'balanced', // 'aggressive', 'balanced', 'quality'
      maxCostPerTask: 0.10, // Maximum cost per task in USD
      fallbackModel: 'anthropic/claude-3-haiku',
      qualityThreshold: 7, // Minimum capability score for quality-sensitive tasks
      enableFallback: true
    };
  }

  /**
   * Select the optimal model for a given task
   * @param {string} taskType - Type of task
   * @param {string} taskDescription - Description of the task
   * @param {Object} context - Additional context
   * @param {Object} options - Override options
   * @returns {Object} Selected model info and reasoning
   */
  async selectModel(taskType, taskDescription, context = {}, options = {}) {
    try {
      // Analyze the task first
      const analysis = await this.taskAnalyzer.analyzeTask(taskType, taskDescription, context);
      
      // Find suitable models
      const candidates = this.findCandidateModels(analysis);
      
      // Select the best model based on cost-effectiveness
      const selectedModel = this.selectBestModel(candidates, analysis, options);
      
      // Track the selection
      this.trackModelSelection(selectedModel, analysis);
      
      return {
        model: selectedModel.id,
        modelInfo: selectedModel,
        analysis,
        reasoning: selectedModel.selectionReason,
        estimatedCost: this.calculateEstimatedCost(selectedModel, analysis),
        alternatives: candidates.slice(0, 3) // Top 3 alternatives
      };
    } catch (error) {
      console.error('Model selection failed:', error);
      return this.getFallbackSelection(taskType, error);
    }
  }

  /**
   * Find candidate models that meet the task requirements
   */
  findCandidateModels(analysis) {
    const candidates = [];
    
    Object.entries(AI_MODELS).forEach(([modelId, model]) => {
      const score = this.scoreModel(model, analysis);
      if (score.meetsRequirements) {
        candidates.push({
          id: modelId,
          ...model,
          score: score.totalScore,
          capabilityMatch: score.capabilityScore,
          costEfficiency: score.costScore,
          meetsRequirements: score.meetsRequirements,
          estimatedCost: this.calculateEstimatedCost(model, analysis)
        });
      }
    });

    // Sort by overall score (higher is better)
    return candidates.sort((a, b) => b.score - a.score);
  }

  /**
   * Score a model against task requirements
   */
  scoreModel(model, analysis) {
    const requiredCapabilities = analysis.requiredCapabilities || [];
    const complexity = analysis.complexity || 2;
    const qualityReq = analysis.qualityRequirement || 'medium';
    
    // Check if model meets minimum capability requirements
    let capabilityScore = 0;
    let meetsMinimum = true;
    
    requiredCapabilities.forEach(capability => {
      const modelCapability = model.capabilities[capability] || 0;
      const requiredLevel = this.getRequiredCapabilityLevel(capability, complexity, qualityReq);
      
      if (modelCapability >= requiredLevel) {
        capabilityScore += modelCapability;
      } else {
        meetsMinimum = false;
      }
    });

    // Calculate cost efficiency score (lower cost = higher score)
    const estimatedCost = this.calculateEstimatedCost(model, analysis);
    const costScore = Math.max(0, 10 - (estimatedCost * 100)); // Scale cost to 0-10

    // Quality bonus for high-quality requirements
    let qualityBonus = 0;
    if (qualityReq === 'high' && model.reliability >= 9) {
      qualityBonus = 2;
    }

    // Speed bonus for urgent tasks
    let speedBonus = 0;
    if (analysis.timeConstraint === 'urgent' && model.speed === 'fast') {
      speedBonus = 1;
    }

    // Calculate total score based on optimization strategy
    let totalScore = 0;
    switch (this.config.costOptimization) {
      case 'aggressive':
        totalScore = (costScore * 0.7) + (capabilityScore * 0.2) + qualityBonus + speedBonus;
        break;
      case 'balanced':
        totalScore = (costScore * 0.4) + (capabilityScore * 0.4) + (model.reliability * 0.2) + qualityBonus + speedBonus;
        break;
      case 'quality':
        totalScore = (capabilityScore * 0.5) + (model.reliability * 0.3) + (costScore * 0.2) + qualityBonus + speedBonus;
        break;
    }

    return {
      totalScore,
      capabilityScore,
      costScore,
      meetsRequirements: meetsMinimum,
      qualityBonus,
      speedBonus
    };
  }

  /**
   * Get required capability level based on task complexity and quality requirements
   */
  getRequiredCapabilityLevel(capability, complexity, qualityReq) {
    const baseLevel = {
      1: 4, // Simple tasks need basic capability
      2: 6, // Medium tasks need good capability
      3: 8  // Complex tasks need high capability
    }[complexity] || 6;

    const qualityAdjustment = {
      'low': -1,
      'medium': 0,
      'high': +2
    }[qualityReq] || 0;

    return Math.max(1, baseLevel + qualityAdjustment);
  }

  /**
   * Select the best model from candidates
   */
  selectBestModel(candidates, analysis, options = {}) {
    if (candidates.length === 0) {
      throw new Error('No suitable models found for task requirements');
    }

    // Apply cost constraints
    const affordableCandidates = candidates.filter(model => 
      model.estimatedCost <= (options.maxCost || this.config.maxCostPerTask)
    );

    const finalCandidates = affordableCandidates.length > 0 ? affordableCandidates : candidates;
    const selected = finalCandidates[0];

    // Add selection reasoning
    selected.selectionReason = this.generateSelectionReason(selected, analysis, finalCandidates);

    return selected;
  }

  /**
   * Generate human-readable reasoning for model selection
   */
  generateSelectionReason(selected, analysis, candidates) {
    const reasons = [];
    
    reasons.push(`Selected for ${analysis.taskCategory} task with complexity level ${analysis.complexity}`);
    reasons.push(`Estimated cost: $${selected.estimatedCost.toFixed(4)}`);
    
    if (selected.capabilityMatch > 8) {
      reasons.push('High capability match for requirements');
    }
    
    if (selected.costEfficiency > 7) {
      reasons.push('Excellent cost efficiency');
    }
    
    if (analysis.qualityRequirement === 'high' && selected.reliability >= 9) {
      reasons.push('High reliability for quality-critical task');
    }
    
    if (candidates.length > 1) {
      const savings = candidates[candidates.length - 1].estimatedCost - selected.estimatedCost;
      if (savings > 0.001) {
        reasons.push(`Saves $${savings.toFixed(4)} vs most expensive option`);
      }
    }

    return reasons.join('. ');
  }

  /**
   * Calculate estimated cost for a model and task
   */
  calculateEstimatedCost(model, analysis) {
    const inputTokens = analysis.estimatedInputTokens || 1000;
    const outputTokens = analysis.estimatedOutputTokens || 1000;
    
    const inputCost = (inputTokens / 1000) * model.costPer1kTokens.input;
    const outputCost = (outputTokens / 1000) * model.costPer1kTokens.output;
    
    return inputCost + outputCost;
  }

  /**
   * Track model selection for analytics
   */
  trackModelSelection(model, analysis) {
    const cost = model.estimatedCost;
    
    this.costTracker.totalCost += cost;
    this.costTracker.modelUsage[model.id] = (this.costTracker.modelUsage[model.id] || 0) + 1;
    this.costTracker.taskCosts.push({
      timestamp: new Date().toISOString(),
      model: model.id,
      cost,
      complexity: analysis.complexity,
      taskCategory: analysis.taskCategory
    });
  }

  /**
   * Get fallback selection when analysis fails
   */
  getFallbackSelection(taskType, error) {
    const fallbackModel = AI_MODELS[this.config.fallbackModel];
    
    return {
      model: this.config.fallbackModel,
      modelInfo: fallbackModel,
      analysis: this.taskAnalyzer.getDefaultAnalysis(taskType),
      reasoning: `Fallback selection due to analysis failure: ${error.message}`,
      estimatedCost: 0.001, // Conservative estimate
      alternatives: [],
      fallback: true
    };
  }

  /**
   * Get cost tracking statistics
   */
  getCostStats() {
    const stats = {
      totalCost: this.costTracker.totalCost,
      totalTasks: this.costTracker.taskCosts.length,
      averageCostPerTask: this.costTracker.totalCost / Math.max(1, this.costTracker.taskCosts.length),
      modelUsage: this.costTracker.modelUsage
    };

    // Calculate cost by complexity
    const costByComplexity = { 1: 0, 2: 0, 3: 0 };
    const countByComplexity = { 1: 0, 2: 0, 3: 0 };
    
    this.costTracker.taskCosts.forEach(task => {
      costByComplexity[task.complexity] += task.cost;
      countByComplexity[task.complexity]++;
    });

    stats.costByComplexity = {
      simple: costByComplexity[1] / Math.max(1, countByComplexity[1]),
      medium: costByComplexity[2] / Math.max(1, countByComplexity[2]),
      complex: costByComplexity[3] / Math.max(1, countByComplexity[3])
    };

    return stats;
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    console.log('Model selector configuration updated:', newConfig);
  }

  /**
   * Reset cost tracking
   */
  resetCostTracking() {
    this.costTracker = {
      totalCost: 0,
      modelUsage: {},
      taskCosts: []
    };
  }
}

module.exports = { AIModelSelector };
